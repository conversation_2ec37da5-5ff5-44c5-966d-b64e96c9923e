import 'package:account_management/account_management.dart';
import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:another_xlider/another_xlider.dart';
import 'package:another_xlider/models/handler.dart';
import 'package:another_xlider/models/slider_step.dart';
import 'package:another_xlider/models/tooltip/tooltip.dart';
import 'package:another_xlider/models/tooltip/tooltip_position_offset.dart';
import 'package:another_xlider/models/trackbar.dart';
import 'package:authentication/widgets/text_form_feild.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';

import '../../custom_widgets/emoji_slider.dart';

class SymptomTrackingPage extends StatefulWidget {
  final String? scrollToSection; // 'pain', 'symptoms', or 'flow'

  const SymptomTrackingPage({Key? key, this.scrollToSection}) : super(key: key);

  @override
  State<SymptomTrackingPage> createState() => _SymptomTrackingPageState();
}

class _SymptomTrackingPageState extends State<SymptomTrackingPage> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _painSectionKey = GlobalKey();
  final GlobalKey _symptomsSectionKey = GlobalKey();
  final GlobalKey _flowSectionKey = GlobalKey();

  List<String> emoji = [
    '\u{1F601}', // 😁
    '\u{1F642}', // 🙂
    '\u{1F610}', // 😐
    '\u{1F615}', // 😕
    '\u{1F641}', // 🙁
    '\u{1F61E}', // 😞
    '\u{1F613}', // 😓
    '\u{1F623}', // 😣
    '\u{1F616}', // 😖
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
  ];

  List<String> pain_description = [
    'No Pain',
    'Discomfort',
    'Very Mild',
    'Mild',
    'Moderate',
    'Significant',
    'High',
    'Very High',
    'Intense',
    'Worst Pain',
    'Worst Pain',
  ];
  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now().add(Duration(days: 6));
  List<SymptomModel> _symptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
  ];
  @override
  void initState() {
    super.initState();
    // Don't reset the selected date - keep the date that was selected in the calendar

    // Scroll to the appropriate section after the widget is built
    if (widget.scrollToSection != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSection(widget.scrollToSection!);
      });
    }
  }

  void _scrollToSection(String section) {
    GlobalKey? targetKey;
    switch (section) {
      case 'pain':
        targetKey = _painSectionKey;
        break;
      case 'symptoms':
        targetKey = _symptomsSectionKey;
        break;
      case 'flow':
        targetKey = _flowSectionKey;
        break;
    }

    if (targetKey?.currentContext != null) {
      Scrollable.ensureVisible(
        targetKey!.currentContext!,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Check if symptoms can be edited for the given date (only today and past dates)
  bool _canEditSymptoms(DateTime date) {
    final today = DateTime.now();
    final dateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = DateTime(today.year, today.month, today.day);

    return dateOnly.isBefore(todayOnly) || dateOnly.isAtSameMomentAs(todayOnly);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {},
      child: BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
        builder: (context, state) {
          return state.maybeMap(
            loading: (_) {
              return Center(
                child: CircularProgressIndicator(),
              );
            },
            data: (state) {
              return Scaffold(
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(.2.sw),
                  child: Container(
                    height: .2.sw,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                            icon: Icon(Icons.arrow_back),
                            onPressed: () {
                              context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent.selectDay(
                                      state.focusedDay
                                          .subtract(Duration(days: 1)),
                                      false));
                            }),
                        Text(
                          DateFormat('EEEE, MMMM d, y')
                              .format(state.focusedDay),
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black),
                        ),
                        IconButton(
                            icon: Icon(Icons.arrow_forward),
                            onPressed: () {
                              context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent.selectDay(
                                      state.focusedDay.add(Duration(days: 1)),
                                      false));
                            }),
                      ],
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: SizedBox(
                    width: 200,
                    height: 50,
                    child: ElevatedButton(
                        onPressed: _canEditSymptoms(state.focusedDay)
                            ? () {
                                context.read<ManagePeriodTrackingBloc>().add(
                                    ManagePeriodTrackingEvent.addPeriodTracking(
                                        state.selectedPeriodTrackingDay));
                                // Close the modal after saving
                                Navigator.of(context).pop();
                              }
                            : null,
                        style: ButtonStyle(
                            backgroundColor: WidgetStatePropertyAll(
                                _canEditSymptoms(state.focusedDay)
                                    ? Color.fromRGBO(58, 38, 101, 1.0)
                                    : Colors.grey)),
                        child: Text(
                          _canEditSymptoms(state.focusedDay)
                              ? 'Save'
                              : 'Cannot edit future dates',
                          style: TextStyle(color: Colors.white),
                        )),
                  ),
                ),
                body: Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: ListView(
                    controller: _scrollController,
                    shrinkWrap: true,
                    children: [
                      SizedBox(height: 20),
                      Container(
                        key: _painSectionKey,
                        margin: EdgeInsets.zero,
                        height: 0.6.sw,
                        width: 0.90.sw,
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color(
                                  0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              blurRadius: 4.0, // the blur radius
                              offset:
                                  Offset(0, 1), // the x,y offset of the shadow
                            ),
                          ],
                          borderRadius: BorderRadius.circular(32),
                          color: Color.fromRGBO(250, 242, 223, 1),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 30.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 10,
                              ),
                              Text(
                                "What is Your Overall Pain?",
                                style: TextStyle(
                                    color: Color.fromRGBO(58, 38, 101, 1.0),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22),
                              ),
                              SizedBox(
                                height: 30,
                              ),
                              Text(
                                state.selectedPeriodTrackingDay.painLevel
                                    .toString(),
                                style: TextStyle(
                                    color: Color.fromRGBO(58, 38, 101, 1.0),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 31),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Container(
                                height: 77,
                                margin: EdgeInsets.zero,
                                child: Align(
                                  child: EmojiSlider(
                                    key: Key('emoji_slider'),
                                    currentValue: state
                                        .selectedPeriodTrackingDay.painLevel!
                                        .toDouble(),
                                    emojis: emoji,
                                    minValue: 0,
                                    maxValue: 10,
                                    labels: pain_description,
                                    onChanged:
                                        (handlerIndex, lowerValue, upperValue) {
                                      context
                                          .read<ManagePeriodTrackingBloc>()
                                          .add(ManagePeriodTrackingEvent
                                              .changePainLevel(
                                                  (lowerValue as double)
                                                      .toInt()));
                                    },
                                  ),
                                ),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'No Pain',
                                      style: TextStyle(color: Colors.black),
                                    ),
                                    Text(
                                      'Worst Pain',
                                      style: TextStyle(color: Colors.black),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Container(
                        key: _symptomsSectionKey,
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color(
                                  0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              blurRadius: 4.0, // the blur radius
                              offset:
                                  Offset(0, 1), // the x,y offset of the shadow
                            ),
                          ],
                          borderRadius: BorderRadius.circular(32),
                          color: Color.fromRGBO(250, 242, 223, 1),
                        ),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Symptoms",
                                style: TextStyle(
                                    color: Color.fromRGBO(58, 38, 101, 1.0),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(15.0),
                                child: GridView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount:
                                        3, // Number of columns in the grid
                                    crossAxisSpacing:
                                        10.0, // Horizontal spacing between items
                                    mainAxisSpacing:
                                        0.0, // Vertical spacing between items
                                    mainAxisExtent: 80.0,
                                    // Aspect ratio for each item
                                  ),
                                  itemCount: _symptoms.length,
                                  itemBuilder: (girdContext, index) {
                                    return GestureDetector(
                                      onTap: _canEditSymptoms(state.focusedDay)
                                          ? () {
                                              context
                                                  .read<
                                                      ManagePeriodTrackingBloc>()
                                                  .add(ManagePeriodTrackingEvent
                                                      .toggleSymptom(
                                                          _symptoms[index]));
                                            }
                                          : null,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Container(
                                            height: 38,
                                            width: 67,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(32),
                                              color: state
                                                      .selectedPeriodTrackingDay
                                                      .symptoms!
                                                      .any((symptom) =>
                                                          symptom.name ==
                                                          _symptoms[index].name)
                                                  ? Color.fromRGBO(
                                                      247, 166, 0, 1)
                                                  : Colors.white,
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(4.0),
                                              child: SvgPicture.asset(
                                                'assets/home/<USER>' ', '_')}.svg',
                                                color: state
                                                        .selectedPeriodTrackingDay
                                                        .symptoms!
                                                        .any((symptom) =>
                                                            symptom.name ==
                                                            _symptoms[index]
                                                                .name)
                                                    ? Colors.white
                                                    : Color.fromRGBO(
                                                        88, 66, 148, 1),
                                                fit: BoxFit.fitHeight,
                                                height: 20,
                                                width: 20,
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                              height:
                                                  4), // Adjust this value to reduce spacing between icon and text
                                          Text(
                                            _symptoms[index].name,
                                            style: TextStyle(
                                              color: Color.fromRGBO(
                                                  58, 38, 101, 1.0),
                                              fontWeight: FontWeight.w700,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ]),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Container(
                        key: _flowSectionKey,
                        height: 120,
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color(
                                  0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              blurRadius: 4.0, // the blur radius
                              offset:
                                  Offset(0, 1), // the x,y offset of the shadow
                            ),
                          ],
                          borderRadius: BorderRadius.circular(32),
                          color: Color.fromRGBO(250, 242, 223, 1),
                        ),
                        child: Column(
                          children: [
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              "Flow",
                              style: TextStyle(
                                  color: Color.fromRGBO(58, 38, 101, 1.0),
                                  fontWeight: FontWeight.w700,
                                  fontSize: 22),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Container(
                                  height: 45,
                                  width: 90,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(32),
                                    color: Color.fromRGBO(250, 242, 223, 1),
                                  ),
                                  child: TextButton(
                                    key: Key('flow_level_1'),
                                    style: ButtonStyle(
                                      padding: WidgetStatePropertyAll(
                                          EdgeInsets.zero),
                                      backgroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              1)
                                          ? WidgetStatePropertyAll(
                                              Color.fromRGBO(247, 166, 0, 1))
                                          : WidgetStatePropertyAll(
                                              Color.fromRGBO(250, 242, 223, 1)),
                                      foregroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              1)
                                          ? WidgetStatePropertyAll(Colors.white)
                                          : WidgetStatePropertyAll(
                                              Colors.purple),
                                    ),
                                    onPressed: () {
                                      context
                                          .read<ManagePeriodTrackingBloc>()
                                          .add(ManagePeriodTrackingEvent
                                              .changeFlowLevel(1));
                                    },
                                    child: SvgPicture.asset(
                                        'assets/home/<USER>',
                                        color: (state.selectedPeriodTrackingDay
                                                    .flowLevel ==
                                                1)
                                            ? Colors.white
                                            : Color.fromRGBO(88, 66, 148, 1)),
                                  ),
                                ),
                                Container(
                                  height: 45,
                                  width: 90,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(32),
                                    color: Color.fromRGBO(250, 242, 223, 1),
                                  ),
                                  child: TextButton(
                                    key: Key('flow_level_2'),
                                    style: ButtonStyle(
                                      padding: WidgetStatePropertyAll(
                                          EdgeInsets.zero),
                                      backgroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              2)
                                          ? WidgetStatePropertyAll(
                                              Color.fromRGBO(247, 166, 0, 1))
                                          : WidgetStatePropertyAll(
                                              Color.fromRGBO(250, 242, 223, 1)),
                                      foregroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              2)
                                          ? WidgetStatePropertyAll(Colors.white)
                                          : WidgetStatePropertyAll(
                                              Colors.purple),
                                    ),
                                    onPressed: () {
                                      context
                                          .read<ManagePeriodTrackingBloc>()
                                          .add(ManagePeriodTrackingEvent
                                              .changeFlowLevel(2));
                                    },
                                    child: SvgPicture.asset(
                                        'assets/home/<USER>',
                                        color: (state.selectedPeriodTrackingDay
                                                    .flowLevel ==
                                                2)
                                            ? Colors.white
                                            : Color.fromRGBO(88, 66, 148, 1)),
                                  ),
                                ),
                                Container(
                                  height: 45,
                                  width: 90,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(32),
                                    color: Color.fromRGBO(250, 242, 223, 1),
                                  ),
                                  child: TextButton(
                                    key: Key('flow_level_3'),
                                    style: ButtonStyle(
                                      padding: WidgetStatePropertyAll(
                                          EdgeInsets.zero),
                                      backgroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              3)
                                          ? WidgetStatePropertyAll(
                                              Color.fromRGBO(247, 166, 0, 1))
                                          : WidgetStatePropertyAll(
                                              Color.fromRGBO(250, 242, 223, 1)),
                                      foregroundColor: (state
                                                  .selectedPeriodTrackingDay
                                                  .flowLevel ==
                                              3)
                                          ? WidgetStatePropertyAll(Colors.white)
                                          : WidgetStatePropertyAll(
                                              Colors.purple),
                                    ),
                                    onPressed: () {
                                      context
                                          .read<ManagePeriodTrackingBloc>()
                                          .add(ManagePeriodTrackingEvent
                                              .changeFlowLevel(3));
                                    },
                                    child: SvgPicture.asset(
                                        'assets/home/<USER>',
                                        color: (state.selectedPeriodTrackingDay
                                                    .flowLevel ==
                                                3)
                                            ? Colors.white
                                            : Color.fromRGBO(88, 66, 148, 1)),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                    ],
                  ),
                ),
              );
            },
            orElse: () {
              return Container();
            },
          );
        },
      ),
    );
  }
}
