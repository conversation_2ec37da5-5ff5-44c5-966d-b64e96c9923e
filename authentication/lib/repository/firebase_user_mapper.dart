import 'dart:convert';

import 'package:authentication/domain/model/user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@lazySingleton
class FirebaseUserMapper {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Method to map a User object from FirebaseAuth to a UserModel object from your domain
  // Now async to accommodate SharedPreferences and Firestore for onboarding status
  Future<UserModel?> toDomain(User _) async {
    // First, try to get onboarding status from Firestore
    bool isOnboarded = false;
    try {
      final userDoc = await _firestore.collection('users').doc(_.uid).get();
      if (userDoc.exists &&
          userDoc.data()?.containsKey('isOnboarded') == true) {
        isOnboarded = userDoc.data()!['isOnboarded'] as bool? ?? false;
      } else {
        // Fallback to SharedPreferences if Firestore doesn't have the field
        final prefs = await SharedPreferences.getInstance();
        final String? onboardedUserIdsJson =
            prefs.getString('onboardedUserIds');
        List<String> onboardedUserIds = [];
        if (onboardedUserIdsJson != null) {
          final decoded = json.decode(onboardedUserIdsJson);
          if (decoded is List) {
            onboardedUserIds = List<String>.from(decoded);
          }
        }
        isOnboarded = onboardedUserIds.contains(_.uid);
      }
    } catch (e) {
      // If Firestore fails, fallback to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final String? onboardedUserIdsJson = prefs.getString('onboardedUserIds');
      List<String> onboardedUserIds = [];
      if (onboardedUserIdsJson != null) {
        final decoded = json.decode(onboardedUserIdsJson);
        if (decoded is List) {
          onboardedUserIds = List<String>.from(decoded);
        }
      }
      isOnboarded = onboardedUserIds.contains(_.uid);
    }

    return UserModel(
        uid: _.uid,
        userName: _.displayName ?? _.email?.split('@').first,
        userEmail: _.email,
        isOnboarded: isOnboarded,
        isEmailVerified: _.emailVerified);
  }
}
